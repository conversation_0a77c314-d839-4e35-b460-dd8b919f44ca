from .models import Notification
from django.conf import settings


def notifications_processor(request):
    """Context processor to add unread notifications count to all templates"""
    notifications_count = 0

    if request.user.is_authenticated:
        notifications_count = Notification.objects.filter(
            user=request.user, is_read=False).count()

    return {
        'notifications_count': notifications_count
    }


def seo_processor(request):
    """Context processor to add SEO-related variables to all templates"""
    return {
        'site_url': settings.SITE_URL,
        'site_name': 'CompletoPLUS',
        'site_description': 'Professional file sharing and project management platform for seamless collaboration and efficient workflow.'
    }
