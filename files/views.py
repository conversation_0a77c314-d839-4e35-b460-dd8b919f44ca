from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, Http404, JsonResponse
from django.contrib import messages
from django.core.exceptions import PermissionDenied
from django.conf import settings
from django.core.files.base import ContentFile
from django.urls import reverse
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db import models
from django.template.loader import render_to_string
from users.utils import send_email_with_logging
from .models import Project, File, Notification, ProgressNote, FileDownload
from .forms import ProjectForm, FileUploadForm, ProgressNoteForm
from .notifications import create_file_upload_notification, create_status_change_notification, create_progress_note_notification
import os
import mimetypes
import zipfile
import tempfile
import logging

# Set up logger
logger = logging.getLogger(__name__)


def send_progress_update_email(project, note, user):
    """
    Send an email notification to the client when a progress update is added
    """
    subject = f'New Update for Project: {project.name}'

    # Context for the email template
    context = {
        'project': project,
        'note': note,
        'user': user,
        'site_url': settings.SITE_URL,
    }

    # Send email with logging
    return send_email_with_logging(
        recipient=project.client,
        email_type='progress_update',
        subject=subject,
        template_name='emails/progress_update.html',
        context=context
    )


def send_project_invitation_email(project, inviter, recipient):
    """
    Send an email notification when a user is invited to a project

    Args:
        project: The project the user is invited to
        inviter: The user who sent the invitation
        recipient: The user who is being invited
    """
    subject = f'You\'ve Been Invited to a Project: {project.name}'

    # Context for the email template
    context = {
        'project': project,
        'inviter': inviter,
        'recipient': recipient,
        'site_url': settings.SITE_URL,
    }

    # Send email with logging
    return send_email_with_logging(
        recipient=recipient,
        email_type='project_invitation',
        subject=subject,
        template_name='emails/project_invitation.html',
        context=context
    )


def send_file_shared_email(file, sender, recipient, message=None):
    """
    Send an email notification when a file is shared with a user

    Args:
        file: The file being shared
        sender: The user who is sharing the file
        recipient: The user who is receiving the file
        message: Optional message from the sender
    """
    subject = f'File Shared With You: {file.file_name}'

    # Context for the email template
    context = {
        'file': file,
        'sender': sender,
        'recipient': recipient,
        'message': message,
        'site_url': settings.SITE_URL,
    }

    # Send email with logging
    return send_email_with_logging(
        recipient=recipient,
        email_type='file_shared',
        subject=subject,
        template_name='emails/file_shared.html',
        context=context
    )


# Function removed to prevent duplicate notifications
# Using the implementation from notifications.py instead


@login_required
def project_list(request):
    """List all projects for the current user"""
    user = request.user

    if user.is_admin_user():
        # Admin can see all projects
        projects = Project.objects.all().order_by('-created_at')
    else:
        # Client can only see their own projects
        projects = Project.objects.filter(client=user).order_by('-created_at')

    return render(request, 'files/project_list.html', {'projects': projects})


@login_required
def project_create(request):
    """Create a new project"""
    is_admin = request.user.is_admin_user()

    if request.method == 'POST':
        form = ProjectForm(request.POST, is_admin=is_admin)
        if form.is_valid():
            try:
                # Create a new project instance
                project = Project()
                project.name = form.cleaned_data['name']
                project.description = form.cleaned_data.get('description', '')

                # Set client and status based on user role
                if is_admin:
                    project.client = form.cleaned_data['client']
                    project.status = form.cleaned_data['status']
                else:
                    project.client = request.user
                    project.status = 'pending'  # Default status for client-created projects

                # Save the project
                project.save()

                # Create notification for the client
                Notification.objects.create(
                    user=project.client,
                    project=project,
                    message=f'New project "{project.name}" has been created.',
                    notification_type='system',
                    data={
                        'project_name': project.name,
                        'created_by': request.user.username,
                        'created_by_id': str(request.user.id),
                        'is_admin_created': is_admin
                    }
                )

                # If admin created project, notify the client
                if is_admin and project.client != request.user:
                    Notification.objects.create(
                        user=project.client,
                        project=project,
                        message=f'Administrator has assigned you a new project: "{project.name}"',
                        notification_type='system',
                        data={
                            'project_name': project.name,
                            'admin_name': request.user.username,
                            'admin_id': str(request.user.id)
                        }
                    )

                    # Send project invitation email
                    try:
                        send_project_invitation_email(
                            project, request.user, project.client)
                        messages.info(
                            request, f'Project invitation email sent to {project.client.email}')
                    except Exception as e:
                        print(f"Error sending project invitation email: {e}")

                messages.success(request, 'Project created successfully!')
                return redirect('project_detail', pk=project.pk)
            except Exception as e:
                messages.error(request, f'Error creating project: {str(e)}')
    else:
        form = ProjectForm(is_admin=is_admin)

    return render(request, 'files/project_form.html', {'form': form, 'action': 'Create'})


@login_required
def project_detail(request, pk):
    """View project details with paginated files"""
    project = get_object_or_404(Project, pk=pk)

    # Check if the user has permission to view this project
    if not request.user.is_admin_user() and project.client != request.user:
        raise PermissionDenied

    # Get all files for this project with filtering
    files_queryset = project.files.all()

    # Apply file type filter if provided
    file_type = request.GET.get('file_type')
    if file_type and file_type != 'all':
        files_queryset = files_queryset.filter(file_type=file_type)

    # Apply date filter if provided
    date_filter = request.GET.get('date')
    if date_filter == 'recent':
        # Last 7 days
        from datetime import datetime, timedelta
        seven_days_ago = datetime.now() - timedelta(days=7)
        files_queryset = files_queryset.filter(uploaded_at__gte=seven_days_ago)

    # Apply search if provided
    search_query = request.GET.get('search')
    if search_query:
        files_queryset = files_queryset.filter(
            models.Q(file_name__icontains=search_query) |
            models.Q(notes__icontains=search_query)
        )

    # Order files by upload date (newest first)
    files_queryset = files_queryset.order_by('-uploaded_at')

    # Pagination for files
    # Default to 10 items per page
    page_size = int(request.GET.get('page_size', 10))
    paginator = Paginator(files_queryset, page_size)
    page_number = request.GET.get('page', 1)

    try:
        files = paginator.page(page_number)
    except PageNotAnInteger:
        # If page is not an integer, deliver first page
        files = paginator.page(1)
    except EmptyPage:
        # If page is out of range, deliver last page
        files = paginator.page(paginator.num_pages)

    # Get progress notes for this project
    progress_notes = project.progress_notes.all()

    # Prepare upload form and progress note form
    upload_form = FileUploadForm()
    progress_form = ProgressNoteForm() if request.user.is_admin_user() else None

    context = {
        'project': project,
        'files': files,
        'upload_form': upload_form,
        'progress_notes': progress_notes,
        'progress_form': progress_form,
        'current_file_type': file_type or 'all',
        'current_date': date_filter or 'all',
        'search_query': search_query or '',
        'page_size': page_size,
        'total_files_count': files_queryset.count(),
    }

    # Handle AJAX requests for infinite scroll/load more
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        try:
            html = render_to_string(
                'files/partials/file_items.html',
                {'files': files, 'project': project}
            )

            # Safely determine if there's a next page and what its number is
            has_next = files.has_next()
            next_page = None
            if has_next:
                try:
                    next_page = files.next_page_number()
                except EmptyPage:
                    # Handle the case where next page doesn't exist
                    has_next = False

            return JsonResponse({
                'html': html,
                'has_next': has_next,
                'next_page': next_page,
            })
        except Exception as e:
            # Log the error and return a graceful error response
            logger.error(f"Error in project_detail AJAX: {str(e)}")
            return JsonResponse({
                'html': '<div class="alert alert-danger">Error loading files. Please refresh the page.</div>',
                'has_next': False,
                'next_page': None,
                'error': str(e)
            }, status=500)

    return render(request, 'files/project_detail.html', context)


@login_required
def project_update(request, pk):
    """Update project status"""
    project = get_object_or_404(Project, pk=pk)

    # Only admin or the project owner (client) can update the project
    if not request.user.is_admin_user() and project.client != request.user:
        raise PermissionDenied

    # Determine if the user is an admin (affects which fields they can edit)
    is_admin = request.user.is_admin_user()

    if request.method == 'POST':
        form = ProjectForm(request.POST, instance=project, is_admin=is_admin)
        if form.is_valid():
            old_status = project.get_status_display()
            old_progress = project.progress

            # Save the form data to the project instance but don't save to DB yet
            project = form.save(commit=False)

            # Let the model's save method handle the progress update based on status
            project.save()

            # Track changes to create a single notification
            changes = []
            notification_text = ""

            # Check for status change
            if old_status != project.get_status_display():
                changes.append(
                    f"Status updated from {old_status} to {project.get_status_display()}")

            # Check for progress change
            if old_progress != project.progress:
                changes.append(
                    f"Progress updated from {old_progress}% to {project.progress}%")

            # If there were changes, create a single progress note and notification
            if changes:
                # Create a combined notification text
                notification_text = "Project update: " + ", ".join(changes)

                # Create a single progress note with the combined message
                progress_note = ProgressNote()
                progress_note.project = project
                progress_note.note = notification_text
                progress_note.created_by = request.user
                progress_note.is_progress_update = True
                progress_note.save()

                # Check for recent notifications to prevent duplicates
                from django.utils import timezone
                import datetime
                time_threshold = timezone.now() - datetime.timedelta(seconds=10)
                recent_notifications = Notification.objects.filter(
                    user=project.client,
                    project=project,
                    notification_type='progress_update',
                    created_at__gte=time_threshold
                )

                if not recent_notifications.exists():
                    # Create only one notification with the combined message
                    create_progress_note_notification(
                        project, notification_text, request.user)
                else:
                    logger.info(
                        f"Skipped duplicate notification for project {project.id}")

            messages.success(request, 'Project updated successfully!')
            return redirect('project_detail', pk=project.pk)
    else:
        form = ProjectForm(instance=project, is_admin=is_admin)

    return render(request, 'files/project_form.html', {'form': form, 'action': 'Update', 'project': project})


@login_required
def add_progress_note(request, project_id):
    """Add a progress note to a project and optionally update project progress/status"""
    project = get_object_or_404(Project, pk=project_id)

    # Only admin can add progress notes
    if not request.user.is_admin_user():
        raise PermissionDenied

    if request.method == 'POST':
        form = ProgressNoteForm(request.POST)
        if form.is_valid():
            # Check if progress or status was updated
            old_progress = project.progress
            old_status = project.status
            progress_changed = False
            status_changed = False

            # Update project progress if provided
            new_progress = None
            if 'progress' in request.POST and request.POST['progress']:
                try:
                    new_progress = int(request.POST['progress'])
                    if 0 <= new_progress <= 100 and new_progress != old_progress:
                        project.progress = new_progress
                        progress_changed = True
                except (ValueError, TypeError):
                    pass  # Ignore invalid progress values

            # Update project status if provided
            new_status = None
            if 'status' in request.POST and request.POST['status']:
                new_status = request.POST['status']
                if new_status in dict(Project.STATUS_CHOICES) and new_status != old_status:
                    project.status = new_status
                    status_changed = True

            # Save project if changes were made
            if progress_changed or status_changed:
                project.save()

            # Get the note text from the form
            note_text = form.cleaned_data.get('note')

            # Create a combined note that includes all changes
            combined_note_text = note_text
            if progress_changed:
                combined_note_text += f"\n\nProject progress updated from {old_progress}% to {project.progress}%."
            if status_changed:
                combined_note_text += f"\n\nProject status updated from {old_status} to {project.get_status_display()}."

            # Save the progress note with the combined text
            note = form.save(commit=False)
            note.project = project
            note.created_by = request.user
            note.note = combined_note_text
            note.is_progress_update = progress_changed
            note.save()

            # Track if we need to send a notification email
            send_notification = True

            # Create only one notification with the combined message
            if send_notification:
                # Create notification for the client - email will be sent by signal handler
                try:
                    # Check for recent notifications to prevent duplicates
                    from django.utils import timezone
                    import datetime
                    time_threshold = timezone.now() - datetime.timedelta(seconds=10)
                    recent_notifications = Notification.objects.filter(
                        user=project.client,
                        project=project,
                        notification_type='progress_update',
                        created_at__gte=time_threshold
                    )

                    if not recent_notifications.exists():
                        create_progress_note_notification(
                            project, combined_note_text, request.user)
                        # Add a message indicating notification was created
                        messages.info(
                            request, f'Notification sent to {project.client.email}')
                    else:
                        logger.info(
                            f"Skipped duplicate notification for project {project.id}")
                except Exception as e:
                    # Log the error but don't stop the process
                    logger.error(f"Error creating notification: {e}")
                    # Add a message indicating notification failed
                    messages.warning(
                        request, 'Could not create notification. Please try again.')

            messages.success(request, 'Progress note added successfully!')

            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'status': 'success',
                    'message': 'Progress note added successfully!',
                    'note_id': str(note.id),
                    'note_text': note.note,
                    'created_at': note.created_at.strftime('%b %d, %Y %H:%M'),
                    'created_by': request.user.username
                })

            return redirect('project_detail', pk=project_id)
        else:
            # Return errors for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'status': 'error', 'errors': form.errors}, status=400)
    else:
        form = ProgressNoteForm()

    return render(request, 'files/progress_note_form.html', {'form': form, 'project': project})


@login_required
def file_upload(request, project_id):
    """Upload a file to a project and automatically zip it"""
    from django.http import JsonResponse
    from .utils import handle_file_upload, get_accepted_file_types
    from django.utils import timezone
    import datetime

    project = get_object_or_404(Project, pk=project_id)

    # Check if the user has permission to upload to this project
    if not request.user.is_admin_user() and project.client != request.user:
        raise PermissionDenied

    if request.method == 'POST':
        # Check for duplicate uploads using upload_id
        upload_id = request.POST.get('upload_id')
        file_name = request.POST.get('file_name')

        # Check for recent uploads with the same name in the last 10 seconds
        # This is a direct check to prevent duplicate file creation
        if file_name:
            time_threshold = timezone.now() - datetime.timedelta(seconds=10)
            recent_files = File.objects.filter(
                project=project,
                owner=request.user,
                file_name=file_name,
                uploaded_at__gte=time_threshold
            )

            if recent_files.exists():
                logger.warning(f"Duplicate file upload detected: {file_name}")

                # Return success for AJAX requests to prevent user confusion
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'status': 'success',
                        'message': 'File uploaded successfully!',
                        'duplicate': True,
                        'redirect': reverse('project_detail', kwargs={'pk': project_id})
                    })

                # Redirect for non-AJAX requests
                messages.info(request, 'File already uploaded.')
                return redirect('project_detail', pk=project_id)

        # Use session to track recent uploads and prevent duplicates
        recent_uploads = request.session.get('recent_uploads', [])

        if upload_id and upload_id in recent_uploads:
            logger.warning(f"Duplicate upload detected with ID: {upload_id}")

            # Return success for AJAX requests to prevent user confusion
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'status': 'success',
                    'message': 'File uploaded successfully!',
                    'duplicate': True,
                    'redirect': reverse('project_detail', kwargs={'pk': project_id})
                })

            # Redirect for non-AJAX requests
            messages.info(request, 'File already uploaded.')
            return redirect('project_detail', pk=project_id)

        form = FileUploadForm(request.POST, request.FILES)
        if form.is_valid():
            # Get the uploaded file(s)
            file_name = form.cleaned_data.get('file_name')
            notes = form.cleaned_data.get('notes')

            # Check if we should zip the file (client-side zipped)
            is_client_zipped = request.POST.get('is_zipped') == 'true'

            # Log the form data for debugging
            logger.info(
                f"File upload form data: file_name={file_name}, is_zipped={is_client_zipped}, upload_id={upload_id}")

            # Handle file upload
            if 'file' in request.FILES:
                # Get the uploaded files
                uploaded_files = request.FILES.getlist('file')

                # Log the upload attempt with more details
                logger.info(
                    f"File upload detected: {len(uploaded_files)} files, is_zipped={request.POST.get('is_zipped')}")

                # Log file names for debugging
                for i, f in enumerate(uploaded_files):
                    logger.info(
                        f"  File {i+1}: {f.name}, size: {f.size} bytes")

                # Check total size of all files
                total_size = sum(f.size for f in uploaded_files)
                if total_size > 104857600:  # 100MB in bytes
                    # Only add error message for non-AJAX requests
                    if request.headers.get('X-Requested-With') != 'XMLHttpRequest':
                        messages.error(
                            request, 'Total file size must be under 100MB')

                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'status': 'error', 'message': 'Total file size must be under 100MB'}, status=400)
                    return redirect('project_detail', pk=project_id)

                # Set file type based on user role
                file_type = 'admin_upload' if request.user.is_admin_user() else 'client_upload'

                # If the file is already zipped on the client side
                if is_client_zipped and len(uploaded_files) == 1:
                    # For client-side zipped files, we expect a single zip file
                    uploaded_file = uploaded_files[0]
                    # Create a File instance for the zip file
                    file_instance = File(
                        project=project,
                        file=uploaded_file,
                        file_name=file_name or os.path.splitext(
                            uploaded_file.name)[0],
                        owner=request.user,
                        notes=notes,
                        file_type=file_type,
                        is_zipped=True,
                        original_file_name="Multiple files zipped client-side"
                    )
                    file_instance.save()

                    # Log the file creation
                    logger.info(
                        f"Created file instance for client-side zipped file: {file_instance.id}")
                else:
                    # Use the utility function to handle file upload and zipping
                    file_instance = handle_file_upload(
                        uploaded_files=uploaded_files,
                        file_name=file_name,
                        notes=notes,
                        owner=request.user,
                        project=project,
                        file_type=file_type
                    )

                    # Log the file creation
                    logger.info(
                        f"Created file instance using handle_file_upload: {file_instance.id}")

                # Create notification and send email
                create_file_upload_notification(
                    request, project, file_instance)

                # Store upload_id in session to prevent duplicate uploads
                if upload_id:
                    recent_uploads = request.session.get('recent_uploads', [])
                    if len(recent_uploads) >= 10:  # Limit the size of the list
                        recent_uploads.pop(0)  # Remove the oldest upload_id
                    recent_uploads.append(upload_id)
                    request.session['recent_uploads'] = recent_uploads
                    request.session.modified = True
                    logger.info(f"Stored upload_id in session: {upload_id}")

                # Only add success message for non-AJAX requests
                if request.headers.get('X-Requested-With') != 'XMLHttpRequest':
                    if len(uploaded_files) == 1:
                        messages.success(
                            request, 'File uploaded successfully!')
                    else:
                        messages.success(
                            request, f'{len(uploaded_files)} files uploaded successfully!')

                # Return JSON response for AJAX requests
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    message = 'File uploaded successfully!' if len(
                        uploaded_files) == 1 else f'{len(uploaded_files)} files uploaded successfully!'
                    return JsonResponse({
                        'status': 'success',
                        'message': message,
                        'file_id': str(file_instance.id),
                        'file_name': file_instance.file_name,
                        'file_url': file_instance.file.url if file_instance.file else None,
                        'file_count': len(uploaded_files),
                        'redirect': reverse('project_detail', kwargs={'pk': project_id})
                    })

                return redirect('project_detail', pk=project_id)
            else:
                # No files were uploaded
                if request.headers.get('X-Requested-With') != 'XMLHttpRequest':
                    messages.error(request, 'No files were selected')

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'status': 'error', 'message': 'No files were selected'}, status=400)

            # Return to the project detail page
            return redirect('project_detail', pk=project_id)
        else:
            # Form validation errors
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'status': 'error', 'message': 'Invalid form data', 'errors': form.errors}, status=400)
    else:
        form = FileUploadForm()

    # Get accepted file types for the template
    accepted_file_types = get_accepted_file_types()

    # Pass the project to the template for context
    return render(request, 'files/file_upload.html', {
        'form': form,
        'project': project,
        'accepted_file_types': accepted_file_types
    })


@login_required
def file_download(request, file_id):
    """Download a file"""
    file_instance = get_object_or_404(File, pk=file_id)
    project = file_instance.project

    # Check if the user has permission to download this file
    if not request.user.is_admin_user() and project.client != request.user:
        raise PermissionDenied

    file_path = file_instance.file.path

    if os.path.exists(file_path):
        # Track the download
        FileDownload.objects.create(
            file=file_instance,
            user=request.user,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT')
        )

        with open(file_path, 'rb') as fh:
            response = HttpResponse(
                fh.read(), content_type=mimetypes.guess_type(file_path)[0])
            response[
                'Content-Disposition'] = f'attachment; filename={os.path.basename(file_path)}'
            return response
    raise Http404


@login_required
def my_files(request):
    """List all files for the current user across all projects"""
    # Get all files for the current user
    files_queryset = File.objects.filter(
        models.Q(owner=request.user) | models.Q(project__client=request.user)
    ).order_by('-uploaded_at')

    # Apply file type filter if provided
    file_type = request.GET.get('file_type')
    if file_type and file_type != 'all':
        files_queryset = files_queryset.filter(file_type=file_type)

    # Apply date filter if provided
    date_filter = request.GET.get('date')
    if date_filter and date_filter != 'all':
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            files_queryset = files_queryset.filter(
                uploaded_at__date=filter_date)
        except ValueError:
            pass

    # Apply search filter if provided
    search_query = request.GET.get('search')
    if search_query:
        files_queryset = files_queryset.filter(
            models.Q(file_name__icontains=search_query) |
            models.Q(notes__icontains=search_query)
        )

    # Set up pagination
    page_size = int(request.GET.get('page_size', 10))
    paginator = Paginator(files_queryset, page_size)
    page = request.GET.get('page', 1)

    try:
        files = paginator.page(page)
    except PageNotAnInteger:
        files = paginator.page(1)
    except EmptyPage:
        files = paginator.page(paginator.num_pages)

    context = {
        'files': files,
        'current_file_type': file_type or 'all',
        'current_date': date_filter or 'all',
        'search_query': search_query or '',
        'page_size': page_size,
        'total_files_count': files_queryset.count(),
    }

    # Handle AJAX requests for infinite scroll/load more
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        try:
            html = render_to_string(
                'files/partials/file_items.html',
                {'files': files}
            )

            # Safely determine if there's a next page and what its number is
            has_next = files.has_next()
            next_page = None
            if has_next:
                try:
                    next_page = files.next_page_number()
                except EmptyPage:
                    # Handle the case where next page doesn't exist
                    has_next = False

            return JsonResponse({
                'html': html,
                'has_next': has_next,
                'next_page': next_page,
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return render(request, 'files/my_files.html', context)


@login_required
def notification_list(request):
    """List all notifications for the current user with pagination and filtering"""
    # Get all notifications for the current user
    notifications_queryset = Notification.objects.filter(user=request.user)

    # Apply filters if provided
    notification_type = request.GET.get('type')
    if notification_type and notification_type != 'all':
        notifications_queryset = notifications_queryset.filter(
            notification_type=notification_type)

    # Filter by read status if provided
    read_status = request.GET.get('read')
    if read_status == 'read':
        notifications_queryset = notifications_queryset.filter(is_read=True)
    elif read_status == 'unread':
        notifications_queryset = notifications_queryset.filter(is_read=False)

    # Apply search if provided
    search_query = request.GET.get('search')
    if search_query:
        notifications_queryset = notifications_queryset.filter(
            models.Q(message__icontains=search_query) |
            models.Q(project__name__icontains=search_query)
        )

    # Order by created_at (newest first)
    notifications_queryset = notifications_queryset.order_by('-created_at')

    # Pagination
    # Default to 10 items per page
    page_size = int(request.GET.get('page_size', 10))
    paginator = Paginator(notifications_queryset, page_size)
    page_number = request.GET.get('page', 1)

    try:
        notifications = paginator.page(page_number)
    except PageNotAnInteger:
        # If page is not an integer, deliver first page
        notifications = paginator.page(1)
    except EmptyPage:
        # If page is out of range, deliver last page
        notifications = paginator.page(paginator.num_pages)

    # Get notification types for filter dropdown
    notification_types = Notification.NOTIFICATION_TYPES

    # Count unread notifications
    unread_count = notifications_queryset.filter(is_read=False).count()

    context = {
        'notifications': notifications,
        'notification_types': notification_types,
        'current_type': notification_type or 'all',
        'current_read': read_status or 'all',
        'search_query': search_query or '',
        'page_size': page_size,
        'unread_count': unread_count,
        'total_count': notifications_queryset.count(),
    }

    # Handle AJAX requests for infinite scroll/load more
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        try:
            html = render_to_string(
                'files/partials/notification_items.html',
                {'notifications': notifications}
            )

            # Safely determine if there's a next page and what its number is
            has_next = notifications.has_next()
            next_page = None
            if has_next:
                try:
                    next_page = notifications.next_page_number()
                except EmptyPage:
                    # Handle the case where next page doesn't exist
                    has_next = False

            return JsonResponse({
                'html': html,
                'has_next': has_next,
                'next_page': next_page,
            })
        except Exception as e:
            # Log the error and return a graceful error response
            logger.error(f"Error in notification_list AJAX: {str(e)}")
            return JsonResponse({
                'html': '<div class="alert alert-danger">Error loading notifications. Please refresh the page.</div>',
                'has_next': False,
                'next_page': None,
                'error': str(e)
            }, status=500)

    return render(request, 'files/notification_list.html', context)


@login_required
def mark_notification_read(request, pk):
    """Mark a notification as read"""
    notification = get_object_or_404(Notification, pk=pk)

    # Check if the notification belongs to the current user
    if notification.user != request.user:
        raise PermissionDenied

    notification.is_read = True
    notification.save()

    # Return JSON response for AJAX requests
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'status': 'success',
            'message': 'Notification marked as read',
            'notification_id': str(notification.id)
        })

    # Redirect for non-AJAX requests
    return redirect('notification_list')


@login_required
def mark_all_notifications_read(request):
    """Mark all notifications as read for the current user"""
    if request.method == 'POST':
        Notification.objects.filter(
            user=request.user, is_read=False).update(is_read=True)

        # Return JSON response for AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'status': 'success',
                'message': 'All notifications marked as read'
            })

        messages.success(request, 'All notifications marked as read')
        return redirect('notification_list')

    # Method not allowed
    return HttpResponse(status=405)


@login_required
def file_preview(request, file_id):
    """Preview a file"""
    file_instance = get_object_or_404(File, pk=file_id)
    project = file_instance.project

    # Check if the user has permission to view this file
    if not request.user.is_admin_user() and project.client != request.user:
        raise PermissionDenied

    # Check if the file can be previewed
    if not file_instance.can_preview:
        raise Http404("This file cannot be previewed")

    file_path = file_instance.file.path

    if os.path.exists(file_path):
        # If the file is zipped, extract it to a temporary location for preview
        if file_instance.is_zipped:
            with tempfile.TemporaryDirectory() as temp_dir:
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    # Extract all files to the temporary directory
                    zip_ref.extractall(temp_dir)

                    # Get the original file name or the first file in the archive
                    if file_instance.original_file_name:
                        original_file = os.path.join(
                            temp_dir, file_instance.original_file_name)
                    else:
                        # Get the first file in the archive
                        files = zip_ref.namelist()
                        if files:
                            original_file = os.path.join(temp_dir, files[0])
                        else:
                            raise Http404("No files found in the archive")

                    # Check if the file exists
                    if not os.path.exists(original_file):
                        # Try to find the file in subdirectories
                        for root, dirs, files in os.walk(temp_dir):
                            for file in files:
                                if file == os.path.basename(file_instance.original_file_name or ''):
                                    original_file = os.path.join(root, file)
                                    break

                    # If the file still doesn't exist, use the first file
                    if not os.path.exists(original_file):
                        for root, dirs, files in os.walk(temp_dir):
                            if files:
                                original_file = os.path.join(root, files[0])
                                break

                    # If we still can't find a file, raise 404
                    if not os.path.exists(original_file):
                        raise Http404("File not found in the archive")

                    # Check if it's a directory
                    if os.path.isdir(original_file):
                        # If it's a directory, try to find the first file in it
                        for root, dirs, files in os.walk(original_file):
                            if files:
                                original_file = os.path.join(root, files[0])
                                break
                        else:
                            # If no files found in the directory
                            raise Http404(
                                "No viewable files found in the directory")

                    # Read the file content
                    with open(original_file, 'rb') as f:
                        file_content = f.read()

                    # Determine the content type
                    content_type = mimetypes.guess_type(
                        original_file)[0] or 'application/octet-stream'

                    # Create the response
                    response = HttpResponse(
                        file_content, content_type=content_type)
                    return response
        else:
            # For non-zipped files, serve directly
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Determine the content type
            content_type = mimetypes.guess_type(
                file_path)[0] or 'application/octet-stream'

            # Create the response
            response = HttpResponse(file_content, content_type=content_type)
            return response

    raise Http404("File not found")


@login_required
def file_delete(request, file_id):
    """Delete a file"""
    file_instance = get_object_or_404(File, pk=file_id)
    project = file_instance.project

    # Check if the user has permission to delete this file
    # Only the file owner or an admin can delete a file
    if file_instance.owner != request.user and not request.user.is_admin_user():
        raise PermissionDenied

    if request.method == 'POST':
        # Delete the file
        file_instance.delete()

        messages.success(request, 'File deleted successfully!')

        # Return JSON response for AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'status': 'success',
                'message': 'File deleted successfully!'
            })

        return redirect('project_detail', pk=project.pk)

    return render(request, 'files/file_confirm_delete.html', {'file': file_instance})


@login_required
def project_delete(request, pk):
    """Delete a project"""
    project = get_object_or_404(Project, pk=pk)

    # Check if the user has permission to delete this project
    # Only the project owner (client) or an admin can delete a project
    if project.client != request.user and not request.user.is_admin_user():
        raise PermissionDenied

    if request.method == 'POST':
        # Delete the project
        project.delete()

        messages.success(request, 'Project deleted successfully!')
        return redirect('project_list')

    return render(request, 'files/project_confirm_delete.html', {'project': project})


@login_required
def batch_download(request):
    """Download multiple files as a zip archive"""
    import zipfile
    import io
    import uuid

    if request.method != 'POST':
        return HttpResponse(status=405)  # Method Not Allowed

    # Get file IDs from request
    file_ids = request.POST.getlist('file_ids')

    if not file_ids:
        messages.error(request, 'No files selected for download')
        return redirect('project_list')

    # Get files that the user has permission to download
    files = []
    for file_id in file_ids:
        try:
            file_instance = File.objects.get(pk=file_id)
            # Check if user has permission to download this file
            if request.user.is_admin_user() or file_instance.project.client == request.user:
                files.append(file_instance)
        except File.DoesNotExist:
            continue

    if not files:
        messages.error(request, 'No files available for download')
        return redirect('project_list')

    # Create a zip file in memory
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for file_instance in files:
            file_path = file_instance.file.path
            if os.path.exists(file_path):
                # Track the download
                FileDownload.objects.create(
                    file=file_instance,
                    user=request.user,
                    ip_address=request.META.get('REMOTE_ADDR'),
                    user_agent=request.META.get('HTTP_USER_AGENT')
                )

                # Add file to zip with a unique name to avoid conflicts
                zip_file.write(file_path, f"{file_instance.file_name}")

    # Generate a unique filename for the zip
    zip_filename = f"files_{uuid.uuid4().hex[:8]}.zip"

    # Return the zip file as a response
    response = HttpResponse(zip_buffer.getvalue(),
                            content_type='application/zip')
    response['Content-Disposition'] = f'attachment; filename={zip_filename}'

    return response


@login_required
def batch_delete(request):
    """Delete multiple files"""
    if request.method != 'POST':
        return HttpResponse(status=405)  # Method Not Allowed

    # Get file IDs from request
    file_ids = request.POST.getlist('file_ids')

    if not file_ids:
        messages.error(request, 'No files selected for deletion')
        return redirect('project_list')

    # Count successfully deleted files
    deleted_count = 0

    # Delete files that the user has permission to delete
    for file_id in file_ids:
        try:
            file_instance = File.objects.get(pk=file_id)
            # Check if user has permission to delete this file
            if file_instance.owner == request.user or request.user.is_admin_user():
                project_id = file_instance.project.id  # Store project ID before deletion
                file_instance.delete()
                deleted_count += 1
        except File.DoesNotExist:
            continue

    if deleted_count > 0:
        messages.success(
            request, f'{deleted_count} file(s) deleted successfully!')
    else:
        messages.error(
            request, 'No files were deleted. You may not have permission to delete the selected files.')

    # Redirect to the project detail page if we have a project ID, otherwise to the project list
    if 'project_id' in locals() and project_id:
        return redirect('project_detail', pk=project_id)
    else:
        return redirect('project_list')
