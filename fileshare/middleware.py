class StrictHTTPSMiddleware:
    """
    Middleware to strictly enforce HTTPS.
    If the request is not secure, return a 403 Forbidden response.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        from django.conf import settings
        from django.http import HttpResponseForbidden, HttpResponseRedirect

        # Only enforce in production
        if not settings.DEBUG:
            # Get the host from the request
            host = request.get_host().lower()

            # Check if the request is secure (either directly or via proxy)
            is_secure = request.is_secure() or (
                'HTTP_X_FORWARDED_PROTO' in request.META and
                request.META['HTTP_X_FORWARDED_PROTO'] == 'https'
            )

            # Block HTTP requests with a 403 Forbidden
            if not is_secure:
                # Determine which HTTPS URL to suggest based on the current host
                if host.startswith('www.'):
                    suggested_url = f"https://{host}"
                else:
                    suggested_url = f"https://www.{host}" if host != 'completoplus.com' else "https://completoplus.com"

                return HttpResponseForbidden(
                    f'<h1>Forbidden</h1><p>HTTPS is required to access this site.</p>'
                    f'<p>Please use <a href="{suggested_url}">{suggested_url}</a></p>'
                )

        return self.get_response(request)


class DomainCanonicalizeMiddleware:
    """
    Middleware to ensure both apex domain and www subdomain work properly.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        from django.conf import settings
        from django.http import HttpResponsePermanentRedirect

        # Only process in production
        if not settings.DEBUG:
            host = request.get_host().lower()

            # If the request is secure and for completoplus.com without www
            # We don't redirect - we want both domains to work
            # This middleware just ensures they both can be accessed

            # Process the request normally
            return self.get_response(request)
        else:
            # In development, just process the request normally
            return self.get_response(request)


class SecurityHeadersMiddleware:
    """
    Middleware to add security headers that also improve SEO
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'

        # Only add HSTS header in production
        from django.conf import settings
        if not settings.DEBUG:
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'

        return response


class AdminAccessMiddleware:
    """
    Middleware to prevent non-admin users from accessing the admin site
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check for the admin URL
        if request.path.startswith('/completoplus-admin/'):
            # If the user is not authenticated, redirect to login
            if not request.user.is_authenticated:
                from django.shortcuts import redirect
                from django.urls import reverse
                login_url = f"{reverse('login')}?next={request.path}"
                return redirect(login_url)

            # Check if the user has admin permissions
            has_admin_permission = False

            # Check superuser and staff status
            if request.user.is_superuser or request.user.is_staff:
                has_admin_permission = True

            # Check user_type
            try:
                if getattr(request.user, 'user_type', '') == 'admin':
                    has_admin_permission = True
            except:
                pass

            # If the user doesn't have admin permissions, redirect to dashboard
            if not has_admin_permission:
                from django.contrib import messages
                from django.shortcuts import redirect
                messages.error(
                    request, 'Access denied. You do not have permission to access the admin panel.')
                return redirect('dashboard')

        # Process the request
        return self.get_response(request)
