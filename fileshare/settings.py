from pathlib import Path
import os
import dj_database_url

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'jf9y@lg=6dyps%n!6&mziti&v+%a*65$u3@b@8t*$3s0ucosr'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False  # Set to False for production

ALLOWED_HOSTS = ['127.0.0.1', 'localhost',
                 'completoplus.com', 'www.completoplus.com',
                 'completoplus.up.railway.app']

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'debug.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
        'users': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'files': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'cloudinary_storage',
    'cloudinary',

    # Custom apps
    'users',
    'files',
    'analysis',
]

# Signal-based asynchronous processing
# This application uses Django signals for asynchronous processing
# instead of Celery/Redis for simplicity and reliability

MIDDLEWARE = [
    # Our strict HTTPS middleware must be first to block non-HTTPS requests immediately
    'fileshare.middleware.StrictHTTPSMiddleware',
    # Domain canonicalization middleware to handle both apex and www domains
    'fileshare.middleware.DomainCanonicalizeMiddleware',
    'django.middleware.security.SecurityMiddleware',
    # Whitenoise middleware for serving static files
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'fileshare.middleware.SecurityHeadersMiddleware',
    # Middleware to restrict admin access to admin users only
    'fileshare.middleware.AdminAccessMiddleware',
]

ROOT_URLCONF = 'fileshare.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'files.context_processors.notifications_processor',
                'files.context_processors.seo_processor',
            ],
        },
    },
]

WSGI_APPLICATION = 'fileshare.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# Database configuration based on environment
if DEBUG:
    # For development, we'll use SQLite for simplicity
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }
else:
    # PostgreSQL configuration for Railway in production using DATABASE_URL
    # Railway automatically sets the DATABASE_URL environment variable
    try:
        # Use direct PostgreSQL credentials for clarity
        DATABASE_URL = 'postgresql://postgres:<EMAIL>:13687/railway'

        # Parse the DATABASE_URL
        DATABASES = {
            'default': dj_database_url.parse(
                DATABASE_URL,
                conn_max_age=300,  # Reduced from 600 to 300 seconds for Railway optimization
                conn_health_checks=True,
            )
        }

        # Print connection info for debugging (without password)
        db_info = DATABASES['default'].copy()
        if 'PASSWORD' in db_info:
            db_info['PASSWORD'] = '********'  # Hide password in logs
        print(f"Database connection info: {db_info}")

    except Exception as e:
        print(f"Error configuring database: {e}")
        # Fallback to SQLite if there's an error
        DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': BASE_DIR / 'db.sqlite3',
            }
        }
        print("Using SQLite as fallback due to database configuration error")

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

# Whitenoise settings for static files
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
WHITENOISE_MAX_AGE = 31536000  # 1 year in seconds

# Media files (Uploaded files)
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')


# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# ============================================================================
# CACHING CONFIGURATION FOR PERFORMANCE
# ============================================================================

# Use database caching for now (can be upgraded to Redis later)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'cache_table',
        'TIMEOUT': 300,  # 5 minutes default timeout
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# Cache middleware settings
CACHE_MIDDLEWARE_ALIAS = 'default'
CACHE_MIDDLEWARE_SECONDS = 300  # 5 minutes
CACHE_MIDDLEWARE_KEY_PREFIX = 'completoplus'

# Custom user model
AUTH_USER_MODEL = 'users.CustomUser'

# Authentication backends - using only one backend to avoid login issues with registration
AUTHENTICATION_BACKENDS = [
    'users.auth_backends.EmailOrUsernameModelBackend',
]

# Login URLs
LOGIN_REDIRECT_URL = 'dashboard'
LOGOUT_REDIRECT_URL = 'home'
LOGIN_URL = 'login'

# Email settings
# Using SMTP backend to send real emails in all environments
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'hpag yiiv gpyv zwrv'  # App password for Gmail

# Default from email
DEFAULT_FROM_EMAIL = '<EMAIL>'
CONTACT_EMAIL = '<EMAIL>'

# Site URL for emails
# Use different URLs for development and production
if DEBUG:
    # Development URL - using port 8001 as we're running on that port
    SITE_URL = 'http://127.0.0.1:8001'
else:
    # Production URL - using custom domain (prefer the apex domain)
    SITE_URL = 'https://completoplus.com'

# Alternative domains that should also work
ALTERNATIVE_DOMAINS = ['www.completoplus.com'] if not DEBUG else []

# File upload settings
# Increase the maximum number of files that can be uploaded at once
DATA_UPLOAD_MAX_NUMBER_FILES = 1000
DATA_UPLOAD_MAX_MEMORY_SIZE = 52428800  # 50MB

# SEO Settings
SEO_SETTINGS = {
    'meta_description': 'CompletoPLUS - Professional file sharing and project management platform for seamless collaboration and efficient workflow.',
    'meta_keywords': 'file sharing, project management, document collaboration, secure file transfer, data analysis',
    'og_image': '/static/img/completoplus-og-image.svg',
    'twitter_image': '/static/img/completoplus-twitter-image.svg',
}

# ============================================================================
# HTTPS ENFORCEMENT & SECURITY SETTINGS
# ============================================================================

# HTTPS Enforcement Mode for StrictHTTPSMiddleware
# Options: 'REDIRECT' (user-friendly) or 'BLOCK' (strict security)
HTTPS_ENFORCEMENT_MODE = 'REDIRECT'  # Change to 'BLOCK' for stricter security

# Core HTTPS enforcement settings
SECURE_SSL_REDIRECT = not DEBUG  # Redirect HTTP to HTTPS in production
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO',
                           'https')  # For Railway.com

# HTTP Strict Transport Security (HSTS) settings
SECURE_HSTS_SECONDS = 31536000  # 1 year - tells browsers to only use HTTPS
SECURE_HSTS_INCLUDE_SUBDOMAINS = True  # Apply HSTS to all subdomains
SECURE_HSTS_PRELOAD = True  # Enable HSTS preload for maximum security

# Additional HTTPS security settings
SECURE_CONTENT_TYPE_NOSNIFF = True  # Prevent MIME type sniffing
SECURE_BROWSER_XSS_FILTER = True  # Enable XSS filtering
# Control referrer information
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Cookie security settings (HTTPS only in production)
SESSION_COOKIE_SECURE = not DEBUG  # Only send session cookies over HTTPS
CSRF_COOKIE_SECURE = not DEBUG  # Only send CSRF cookies over HTTPS
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access to session cookies
CSRF_COOKIE_HTTPONLY = True  # Prevent JavaScript access to CSRF cookies
SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
CSRF_COOKIE_SAMESITE = 'Lax'  # CSRF protection

# CSRF settings for production - HTTPS only
CSRF_TRUSTED_ORIGINS = [
    'https://completoplus.up.railway.app',
    'https://completoplus.com',
    'https://www.completoplus.com',
    'https://*.up.railway.app',  # Allow all Railway subdomains
]

# Additional security settings
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'  # Prevent cross-origin attacks
X_FRAME_OPTIONS = 'DENY'  # Prevent clickjacking attacks

# Admin Settings
ADMIN_SITE_TITLE = 'CompletoPLUS Admin'
ADMIN_SITE_HEADER = 'CompletoPLUS Administration'
CUSTOM_ADMIN_URL = 'completoplus-admin'

# Cloudinary settings
CLOUDINARY_STORAGE = {
    'CLOUD_NAME': 'dl82v3a1z',
    'API_KEY': '471254897293575',
    'API_SECRET': 'WFyEf0H_ttsqhXY6GO_PdT5zbew',
}

# Enable folder creation in debug mode
CLOUDINARY_CREATE_FOLDERS_IN_DEBUG = False

# Use Cloudinary for media files in production
if not DEBUG:
    DEFAULT_FILE_STORAGE = 'files.storage.CompletoPLUSCloudinaryStorage'
    # Using whitenoise for static files in production
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
else:
    # Using Cloudinary in development for testing
    DEFAULT_FILE_STORAGE = 'files.storage.CompletoPLUSCloudinaryStorage'
