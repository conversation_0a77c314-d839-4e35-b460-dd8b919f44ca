from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count
from django.utils.safestring import mark_safe
from django.shortcuts import redirect
from .models import CustomUser, DashboardSettings, EmailLog, Testimonial
from .utils import get_email_stats


class DashboardSettingsInline(admin.StackedInline):
    model = DashboardSettings
    can_delete = False
    verbose_name_plural = 'Dashboard Settings'
    fk_name = 'user'
    fieldsets = (
        ('Appearance', {
            'fields': ('layout', 'theme', 'color_scheme')
        }),
        ('Widgets', {
            'fields': ('show_recent_files', 'show_projects', 'show_statistics', 'show_activity')
        }),
        ('Notifications', {
            'fields': ('email_notifications',)
        }),
        ('Other Settings', {
            'fields': ('items_per_page',)
        }),
    )


class CustomUserAdmin(UserAdmin):
    model = CustomUser
    list_display = ['username', 'email', 'first_name', 'last_name',
                    'user_type_badge', 'project_count', 'date_joined', 'is_active']
    list_filter = ['user_type', 'is_staff', 'is_active', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    readonly_fields = ['date_joined', 'last_login']
    inlines = [DashboardSettingsInline]

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'email')}),
        ('User Type', {'fields': ('user_type',)}),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'user_type'),
        }),
    )

    def user_type_badge(self, obj):
        if obj.user_type == 'admin':
            return format_html('<span style="background-color: #dc3545; color: white; padding: 3px 8px; border-radius: 4px;">Admin</span>')
        else:
            return format_html('<span style="background-color: #007bff; color: white; padding: 3px 8px; border-radius: 4px;">Client</span>')

    user_type_badge.short_description = 'User Type'

    def project_count(self, obj):
        if obj.user_type == 'client':
            count = obj.projects.count()
            url = reverse('admin:files_project_changelist') + \
                f'?client__id__exact={obj.id}'
            return format_html('<a href="{}">{} projects</a>', url, count)
        return '-'

    project_count.short_description = 'Projects'

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            project_count=Count('projects', distinct=True))
        return queryset

    def get_inline_instances(self, request, obj=None):
        if not obj:
            return []
        return super().get_inline_instances(request, obj)

    def has_module_permission(self, request):
        # Allow staff and admin users to access the module
        return True

    def has_view_permission(self, request, obj=None):
        # Users can view their own profile
        if obj and obj == request.user:
            return True
        # Admin users can view all profiles
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_add_permission(self, request):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_change_permission(self, request, obj=None):
        # Users can edit their own profile
        if obj and obj == request.user:
            return True
        # Admin users can edit all profiles
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_delete_permission(self, request, obj=None):
        # Only staff or admin users can delete profiles
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())


# Register the CustomUser model with the admin site
admin.site.register(CustomUser, CustomUserAdmin)


class EmailLogAdmin(admin.ModelAdmin):
    list_display = ['email_type', 'recipient_email',
                    'subject', 'sent_at', 'status']
    list_filter = ['email_type', 'status', 'sent_at']
    search_fields = ['recipient__email', 'recipient__username', 'subject']
    readonly_fields = ['id', 'recipient', 'email_type', 'subject',
                       'sent_at', 'status', 'error_message', 'email_stats']
    date_hierarchy = 'sent_at'

    def recipient_email(self, obj):
        return obj.recipient.email

    recipient_email.short_description = 'Recipient'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def email_stats(self, obj):
        stats = get_email_stats()
        html = '<div style="padding: 10px; background-color: #f8f9fa; border-radius: 5px;">'
        html += f'<h3>Email Statistics</h3>'
        html += f'<p><strong>Total Emails:</strong> {stats["total"]}</p>'
        html += f'<p><strong>Successful:</strong> {stats["successful"]} ({stats["success_rate"]:.1f}%)</p>'
        html += f'<p><strong>Failed:</strong> {stats["failed"]}</p>'

        html += '<h4>By Email Type</h4>'
        html += '<table style="width: 100%; border-collapse: collapse;">'
        html += '<tr><th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Type</th><th style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd;">Count</th></tr>'

        for email_type, count in stats['by_type'].items():
            # Get display name for email type
            display_name = dict(EmailLog.EMAIL_TYPES).get(
                email_type, email_type)
            html += f'<tr><td style="padding: 8px; border-bottom: 1px solid #ddd;">{display_name}</td><td style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd;">{count}</td></tr>'

        html += '</table>'
        html += '</div>'

        return mark_safe(html)

    email_stats.short_description = 'Email Statistics'

    def changelist_view(self, request, extra_context=None):
        # Add email stats to the changelist view
        if not extra_context:
            extra_context = {}
        extra_context['email_stats'] = get_email_stats()
        return super().changelist_view(request, extra_context=extra_context)


class TestimonialAdmin(admin.ModelAdmin):
    list_display = ['client_name', 'service_type', 'rating_stars',
                    'created_at', 'is_approved', 'is_featured', 'approval_actions']
    list_filter = ['rating', 'service_type',
                   'is_approved', 'is_featured', 'created_at']
    search_fields = ['client__username', 'client__email',
                     'client__first_name', 'client__last_name', 'content']
    readonly_fields = ['client', 'service_type',
                       'rating', 'content', 'created_at']
    date_hierarchy = 'created_at'
    actions = ['approve_testimonials', 'unapprove_testimonials',
               'feature_testimonials', 'unfeature_testimonials']
    list_editable = ['is_featured']

    def client_name(self, obj):
        return obj.client.get_full_name()

    client_name.short_description = 'Client'

    def rating_stars(self, obj):
        stars = '★' * obj.rating + '☆' * (5 - obj.rating)
        return format_html('<span style="color: #ffc107;">{}</span>', stars)

    rating_stars.short_description = 'Rating'

    def approval_actions(self, obj):
        actions = []

        # Approval/Unapproval action
        if obj.is_approved:
            actions.append(
                format_html(
                    '<a class="button" href="{}" style="background-color: #dc3545; color: white; padding: 3px 8px; border-radius: 4px; text-decoration: none; margin-right: 5px;">'
                    'Unapprove</a>',
                    reverse('admin:unapprove_testimonial', args=[obj.pk])
                )
            )
        else:
            actions.append(
                format_html(
                    '<a class="button" href="{}" style="background-color: #28a745; color: white; padding: 3px 8px; border-radius: 4px; text-decoration: none; margin-right: 5px;">'
                    'Approve</a>',
                    reverse('admin:approve_testimonial', args=[obj.pk])
                )
            )

        # Feature/Unfeature action
        if obj.is_featured:
            actions.append(
                format_html(
                    '<a class="button" href="{}" style="background-color: #6c757d; color: white; padding: 3px 8px; border-radius: 4px; text-decoration: none;">'
                    'Unfeature</a>',
                    reverse('admin:unfeature_testimonial', args=[obj.pk])
                )
            )
        else:
            actions.append(
                format_html(
                    '<a class="button" href="{}" style="background-color: #ffc107; color: #212529; padding: 3px 8px; border-radius: 4px; text-decoration: none;">'
                    'Feature</a>',
                    reverse('admin:feature_testimonial', args=[obj.pk])
                )
            )

        return format_html(''.join(actions))

    approval_actions.short_description = 'Actions'

    def approve_testimonials(self, request, queryset):
        updated = queryset.update(is_approved=True)
        self.message_user(request, f'{updated} testimonials were approved.')

    approve_testimonials.short_description = 'Approve selected testimonials'

    def unapprove_testimonials(self, request, queryset):
        updated = queryset.update(is_approved=False)
        self.message_user(request, f'{updated} testimonials were unapproved.')

    unapprove_testimonials.short_description = 'Unapprove selected testimonials'

    def feature_testimonials(self, request, queryset):
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} testimonials were featured.')

    feature_testimonials.short_description = 'Feature selected testimonials'

    def unfeature_testimonials(self, request, queryset):
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} testimonials were unfeatured.')

    unfeature_testimonials.short_description = 'Unfeature selected testimonials'

    def get_urls(self):
        from django.urls import path
        urls = super().get_urls()
        custom_urls = [
            path(
                '<uuid:testimonial_id>/approve/',
                self.admin_site.admin_view(self.approve_testimonial),
                name='approve_testimonial',
            ),
            path(
                '<uuid:testimonial_id>/unapprove/',
                self.admin_site.admin_view(self.unapprove_testimonial),
                name='unapprove_testimonial',
            ),
            path(
                '<uuid:testimonial_id>/feature/',
                self.admin_site.admin_view(self.feature_testimonial),
                name='feature_testimonial',
            ),
            path(
                '<uuid:testimonial_id>/unfeature/',
                self.admin_site.admin_view(self.unfeature_testimonial),
                name='unfeature_testimonial',
            ),
        ]
        return custom_urls + urls

    def approve_testimonial(self, request, testimonial_id):
        testimonial = self.get_object(request, testimonial_id)
        testimonial.is_approved = True
        testimonial.save()
        self.message_user(
            request, f'Testimonial by {testimonial.client.get_full_name()} has been approved.')
        return redirect('admin:users_testimonial_changelist')

    def unapprove_testimonial(self, request, testimonial_id):
        testimonial = self.get_object(request, testimonial_id)
        testimonial.is_approved = False
        testimonial.save()
        self.message_user(
            request, f'Testimonial by {testimonial.client.get_full_name()} has been unapproved.')
        return redirect('admin:users_testimonial_changelist')

    def feature_testimonial(self, request, testimonial_id):
        testimonial = self.get_object(request, testimonial_id)
        testimonial.is_featured = True
        testimonial.save()
        self.message_user(
            request, f'Testimonial by {testimonial.client.get_full_name()} has been featured.')
        return redirect('admin:users_testimonial_changelist')

    def unfeature_testimonial(self, request, testimonial_id):
        testimonial = self.get_object(request, testimonial_id)
        testimonial.is_featured = False
        testimonial.save()
        self.message_user(
            request, f'Testimonial by {testimonial.client.get_full_name()} has been unfeatured.')
        return redirect('admin:users_testimonial_changelist')
