"""
Custom admin actions for optimized bulk operations in CompletoPLUS.
"""
from django.contrib import admin
from django.contrib.admin.actions import delete_selected
from django.db import transaction
from django.contrib import messages
from django.utils.html import format_html
from django.core.exceptions import PermissionDenied
import logging
import time

logger = logging.getLogger(__name__)


def optimized_bulk_delete_users(modeladmin, request, queryset):
    """
    Optimized bulk delete for users that handles Cloudinary cleanup efficiently.
    """
    if not modeladmin.has_delete_permission(request):
        raise PermissionDenied
    
    user_count = queryset.count()
    
    if user_count == 0:
        messages.warning(request, "No users selected for deletion.")
        return
    
    # Collect Cloudinary folders to delete before deleting users
    cloudinary_folders = []
    client_users = []
    
    for user in queryset:
        if hasattr(user, 'is_client_user') and user.is_client_user():
            folder_path = f"COMPLETOPLUS/{user.username}_{user.id}"
            cloudinary_folders.append(folder_path)
            client_users.append(user.username)
    
    start_time = time.time()
    
    try:
        with transaction.atomic():
            # Temporarily disable the Cloudinary deletion signal to prevent individual deletions
            from django.db.models.signals import pre_delete
            from users.signals import delete_user_cloudinary_folder
            from users.models import CustomUser
            
            # Disconnect the signal
            pre_delete.disconnect(delete_user_cloudinary_folder, sender=CustomUser)
            
            try:
                # Perform the bulk deletion
                deleted_count, deleted_details = queryset.delete()
                
                # Re-connect the signal
                pre_delete.connect(delete_user_cloudinary_folder, sender=CustomUser)
                
                # Now handle Cloudinary cleanup in batch
                if cloudinary_folders:
                    from files.storage import CompletoPLUSCloudinaryStorage
                    storage = CompletoPLUSCloudinaryStorage()
                    
                    # Delete Cloudinary folders in batch (more efficient)
                    for folder_path in cloudinary_folders:
                        try:
                            storage.delete_folder_by_path(folder_path)
                            logger.info(f"Deleted Cloudinary folder: {folder_path}")
                        except Exception as e:
                            logger.warning(f"Failed to delete Cloudinary folder {folder_path}: {e}")
                
                delete_time = time.time() - start_time
                
                # Success message
                messages.success(
                    request,
                    format_html(
                        "Successfully deleted <strong>{}</strong> users in {:.2f} seconds. "
                        "Cloudinary folders cleaned up for {} client users.",
                        deleted_count,
                        delete_time,
                        len(client_users)
                    )
                )
                
                logger.info(f"Bulk deleted {deleted_count} users in {delete_time:.2f}s")
                
            except Exception as e:
                # Re-connect the signal in case of error
                pre_delete.connect(delete_user_cloudinary_folder, sender=CustomUser)
                raise e
                
    except Exception as e:
        messages.error(
            request,
            f"Error during bulk deletion: {str(e)}"
        )
        logger.error(f"Bulk delete failed: {e}")


def optimized_bulk_delete_projects(modeladmin, request, queryset):
    """
    Optimized bulk delete for projects with proper file cleanup.
    """
    if not modeladmin.has_delete_permission(request):
        raise PermissionDenied
    
    project_count = queryset.count()
    
    if project_count == 0:
        messages.warning(request, "No projects selected for deletion.")
        return
    
    start_time = time.time()
    
    try:
        with transaction.atomic():
            # Get file count before deletion for reporting
            total_files = sum(project.files.count() for project in queryset)
            
            # Perform the bulk deletion (CASCADE will handle related objects)
            deleted_count, deleted_details = queryset.delete()
            
            delete_time = time.time() - start_time
            
            messages.success(
                request,
                format_html(
                    "Successfully deleted <strong>{}</strong> projects and <strong>{}</strong> "
                    "associated files in {:.2f} seconds.",
                    deleted_count,
                    total_files,
                    delete_time
                )
            )
            
            logger.info(f"Bulk deleted {deleted_count} projects with {total_files} files in {delete_time:.2f}s")
            
    except Exception as e:
        messages.error(
            request,
            f"Error during bulk deletion: {str(e)}"
        )
        logger.error(f"Project bulk delete failed: {e}")


def optimized_bulk_delete_files(modeladmin, request, queryset):
    """
    Optimized bulk delete for files with Cloudinary cleanup.
    """
    if not modeladmin.has_delete_permission(request):
        raise PermissionDenied
    
    file_count = queryset.count()
    
    if file_count == 0:
        messages.warning(request, "No files selected for deletion.")
        return
    
    start_time = time.time()
    
    try:
        with transaction.atomic():
            # Collect Cloudinary file paths before deletion
            cloudinary_files = []
            for file_obj in queryset:
                if hasattr(file_obj.file, 'public_id'):
                    cloudinary_files.append(file_obj.file.public_id)
            
            # Perform the bulk deletion
            deleted_count, deleted_details = queryset.delete()
            
            # Clean up Cloudinary files if needed
            if cloudinary_files:
                from files.storage import CompletoPLUSCloudinaryStorage
                storage = CompletoPLUSCloudinaryStorage()
                
                for public_id in cloudinary_files:
                    try:
                        storage.delete(public_id)
                        logger.info(f"Deleted Cloudinary file: {public_id}")
                    except Exception as e:
                        logger.warning(f"Failed to delete Cloudinary file {public_id}: {e}")
            
            delete_time = time.time() - start_time
            
            messages.success(
                request,
                format_html(
                    "Successfully deleted <strong>{}</strong> files in {:.2f} seconds.",
                    deleted_count,
                    delete_time
                )
            )
            
            logger.info(f"Bulk deleted {deleted_count} files in {delete_time:.2f}s")
            
    except Exception as e:
        messages.error(
            request,
            f"Error during bulk deletion: {str(e)}"
        )
        logger.error(f"File bulk delete failed: {e}")


def chunked_bulk_delete(modeladmin, request, queryset, chunk_size=100):
    """
    Chunked bulk delete for very large datasets to prevent memory issues.
    """
    if not modeladmin.has_delete_permission(request):
        raise PermissionDenied
    
    total_count = queryset.count()
    
    if total_count == 0:
        messages.warning(request, "No items selected for deletion.")
        return
    
    start_time = time.time()
    deleted_total = 0
    
    try:
        # Process in chunks to avoid memory issues
        while queryset.exists():
            chunk = queryset[:chunk_size]
            chunk_ids = list(chunk.values_list('id', flat=True))
            
            with transaction.atomic():
                deleted_count, _ = queryset.filter(id__in=chunk_ids).delete()
                deleted_total += deleted_count
        
        delete_time = time.time() - start_time
        
        messages.success(
            request,
            format_html(
                "Successfully deleted <strong>{}</strong> items in {:.2f} seconds using chunked deletion.",
                deleted_total,
                delete_time
            )
        )
        
        logger.info(f"Chunked bulk deleted {deleted_total} items in {delete_time:.2f}s")
        
    except Exception as e:
        messages.error(
            request,
            f"Error during chunked bulk deletion: {str(e)}"
        )
        logger.error(f"Chunked bulk delete failed: {e}")


# Action descriptions
optimized_bulk_delete_users.short_description = "Delete selected users (optimized)"
optimized_bulk_delete_projects.short_description = "Delete selected projects (optimized)"
optimized_bulk_delete_files.short_description = "Delete selected files (optimized)"
chunked_bulk_delete.short_description = "Delete selected items (chunked for large datasets)"
