import logging
from django.conf import settings
from .models import Email<PERSON>og, CustomUser

logger = logging.getLogger(__name__)


def send_email_with_logging(recipient, email_type, subject, template_name, context, force_send=False):
    """
    Send an email directly and log it in the database.
    Uses Django's built-in email functionality.

    Args:
        recipient: CustomUser object or email string
        email_type: Type of email (from EmailLog.EMAIL_TYPES)
        subject: Email subject
        template_name: Path to the email template
        context: Dictionary of context variables for the template
        force_send: If True, send the email regardless of user preferences

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    logger.info(f"Processing {email_type} email to {recipient}")

    try:
        # Ensure we have a CustomUser object
        if isinstance(recipient, str):
            try:
                recipient_user = CustomUser.objects.get(email=recipient)
                logger.info(f"Found user object for email {recipient}")
            except CustomUser.DoesNotExist:
                # Can't log if user doesn't exist
                logger.error(f"User with email {recipient} does not exist")
                return False
        else:
            recipient_user = recipient
            logger.info(
                f"Using provided user object with email {recipient_user.email}")

        # Check user preferences unless force_send is True
        if not force_send:
            # Check email preferences based on email type
            if email_type == 'progress_update' and not recipient_user.receive_project_emails:
                logger.info(
                    f"Skipping progress_update email to {recipient_user.email} (user opted out)")
                return False

            if email_type == 'file_upload' and not recipient_user.receive_file_emails:
                logger.info(
                    f"Skipping file_upload email to {recipient_user.email} (user opted out)")
                return False

            if email_type == 'comment' and not recipient_user.receive_comment_emails:
                logger.info(
                    f"Skipping comment email to {recipient_user.email} (user opted out)")
                return False

            if email_type == 'marketing' and not recipient_user.receive_marketing_emails:
                logger.info(
                    f"Skipping marketing email to {recipient_user.email} (user opted out)")
                return False

        # Create a log entry for the email
        try:
            email_log = EmailLog.objects.create(
                recipient=recipient_user,
                email_type=email_type,
                subject=subject,
                status='queued'
            )
            logger.info(f"Email logged with ID {email_log.id}")
        except Exception as log_error:
            logger.error(f"Error logging email: {log_error}")
            # Continue even if logging fails

        # Direct email sending
        try:
            # Import necessary modules
            from django.core.mail import send_mail
            from django.template.loader import render_to_string
            from django.utils.html import strip_tags

            # Render the email template
            html_message = render_to_string(template_name, context)
            plain_message = strip_tags(html_message)

            # Send the email directly
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient_user.email],
                html_message=html_message,
                fail_silently=False,
            )

            # Update the email log
            if 'email_log' in locals():
                email_log.status = 'success'
                email_log.save(update_fields=['status'])

            logger.info(f"Email sent directly to {recipient_user.email}")
            return True
        except Exception as e:
            # Update the email log with failure
            if 'email_log' in locals():
                email_log.status = 'failed'
                email_log.error_message = str(e)
                email_log.save(update_fields=['status', 'error_message'])

            logger.error(f"Failed to send email directly: {e}")
            return False

    except Exception as e:
        logger.error(f"Unexpected error in send_email_with_logging: {e}")
        return False


def get_email_stats():
    """
    Get statistics about emails sent

    Returns:
        dict: Email statistics
    """
    total_emails = EmailLog.objects.count()
    successful_emails = EmailLog.objects.filter(status='success').count()
    failed_emails = EmailLog.objects.filter(status='failed').count()

    # Get counts by email type
    email_types = {}
    for email_type, _ in EmailLog.EMAIL_TYPES:
        email_types[email_type] = EmailLog.objects.filter(
            email_type=email_type).count()

    return {
        'total': total_emails,
        'successful': successful_emails,
        'failed': failed_emails,
        'success_rate': (successful_emails / total_emails * 100) if total_emails > 0 else 0,
        'by_type': email_types
    }
